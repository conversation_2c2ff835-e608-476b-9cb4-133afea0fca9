import { useBoolean } from 'minimal-shared/hooks';
import {
  useRef,
  useMemo,
  useState,
  useEffect,
  forwardRef,
  useCallback,
  useImperativeHandle,
} from 'react';

import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import {
  Box,
  Card,
  Table,
  Button,
  Select,
  Switch,
  Checkbox,
  MenuItem,
  TableRow,
  TableBody,
  TableCell,
  Pagination,
  Typography,
  FormControl,
  FormControlLabel,
} from '@mui/material';

import { useTableColumnSettings } from 'src/hooks/use-table-column-settings';

import { useTranslate } from 'src/locales';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { ConfirmDialog } from 'src/components/custom-dialog';
import {
  useTable,
  TableNoData,
  TableSkeleton,
  useTableSearch,
  TableSearchBar,
  TableHeadCustom,
  TableColumnManager,
  useColumnVisibility,
} from 'src/components/table';

/**
 * 通用分页表格组件
 * @param {string=} tableId - 表格唯一标识符，用于列设置持久化
 * @param {Object[]} columns - 列定义数组，支持id/label/align/width/minWidth/maxWidth/renderCell/sortable等属性
 * @param {function} getData - 获取数据的异步函数，签名：(page, rowsPerPage, orderBy, order, searchValue) => Promise<[dataArray, totalCount]>
 * @param {function=} onRowClick - 行点击事件回调，参数为row对象
 * @param {boolean=} enableSelection - 是否启用行选择，默认true
 * @param {function=} onSelectionChange - 选择变化回调，参数为selectedIds数组
 * @param {function=} onDelete - 批量删除回调，参数为selectedIds数组
 * @param {Array=} selectedActions - 选中项操作按钮数组，每个项包含{key, label, icon, onClick, color}等属性
 * @param {boolean=} loading - 外部控制加载状态
 * @param {string=} noDataText - 无数据时显示的文本
 * @param {Object=} tableProps - 传递给Table组件的额外属性
 * @param {boolean=} enableStickyLastColumn - 是否启用最后一列固定，默认true
 */
export const TableView = forwardRef(function TableView(
  {
    tableId,
    columns,
    getData,
    onRowClick,
    enableSelection = true,
    onSelectionChange,
    onDelete,
    selectedActions = [],
    loading: externalLoading,
    noDataText,
    tableProps = {},
    enableStickyLastColumn = true,
    enableAutoResizeRowsPerPage = true, // 默认关闭自动调整分页大小，避免窗口大小变化时频繁查询
  },
  ref
) {
  const { t } = useTranslate('common');
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
  const isXL = useMediaQuery(theme.breakpoints.up('xl'));
  const is2K = useMediaQuery('(min-width:1920px)');
  const is4K = useMediaQuery('(min-width:2560px)');

  // 根据屏幕尺寸动态计算建议的分页大小
  const getSuggestedRowsPerPage = useCallback(() => {
    if (is4K) return 50;
    if (is2K) return 30;
    if (isXL) return 25;
    if (isDesktop) return 20;
    return 10;
  }, [is4K, is2K, isXL, isDesktop]);

  // 记录用户是否手动选择过分页大小
  const [hasUserSelectedRowsPerPage, setHasUserSelectedRowsPerPage] = useState(false);

  const table = useTable({
    defaultDense: true,
    defaultRowsPerPage: getSuggestedRowsPerPage(),
    defaultCurrentPage: 0, // MUI分页从0开始，不是1
    defaultOrderBy: 'id', // 默认按 id 排序
    defaultOrder: 'desc', // 默认降序
  });

  // 防抖处理屏幕尺寸变化，避免频繁调整分页大小
  const [debouncedScreenSize, setDebouncedScreenSize] = useState({
    is4K,
    is2K,
    isXL,
    isDesktop,
  });

  useEffect(() => {
    // 只有在启用自动调整时才进行防抖处理
    if (!enableAutoResizeRowsPerPage) return undefined;

    const timer = setTimeout(() => {
      setDebouncedScreenSize({ is4K, is2K, isXL, isDesktop });
    }, 300); // 300ms 防抖延迟

    return () => clearTimeout(timer);
  }, [is4K, is2K, isXL, isDesktop, enableAutoResizeRowsPerPage]);

  // 当屏幕尺寸变化时，如果启用自动调整且用户没有手动选择，则自动调整分页大小（防抖后）
  useEffect(() => {
    if (!enableAutoResizeRowsPerPage || hasUserSelectedRowsPerPage) return;

    const suggestedSize = getSuggestedRowsPerPage();
    if (table.rowsPerPage !== suggestedSize) {
      table.setRowsPerPage(suggestedSize);
      table.setPage(0); // 重置到第一页
    }
  }, [
    debouncedScreenSize,
    hasUserSelectedRowsPerPage,
    getSuggestedRowsPerPage,
    table,
    enableAutoResizeRowsPerPage,
  ]);

  const confirmDialog = useBoolean();
  const [tableData, setTableData] = useState([]);
  const [showNoData, setShowNoData] = useState(false); // 初始不显示无数据，避免闪现
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);

  // 使用搜索Hook
  const tableSearch = useTableSearch('');

  // 使用列可见性Hook
  const columnManager = useColumnVisibility(columns);

  // 根据是否提供tableId决定使用持久化还是内存状态
  const usePersistentColumns = Boolean(tableId);

  // 使用持久化Hook（仅在提供tableId时启用）
  const persistentColumnSettings = useTableColumnSettings(tableId, columns);

  // 添加调试信息
  if (process.env.NODE_ENV === 'development' && tableId) {
    console.log(
      `[TableView] 初始化 - tableId: ${tableId}, usePersistentColumns: ${usePersistentColumns}`
    );
    console.log(`[TableView] persistentColumnSettings:`, persistentColumnSettings);
    console.log(
      `[TableView] persistentColumnSettings._updateKey: ${persistentColumnSettings._updateKey}`
    );
  }

  // 添加强制刷新状态，确保 TableView 能响应 persistentColumnSettings 的变化
  const [tableViewUpdateKey, setTableViewUpdateKey] = useState(0);

  // 监听 persistentColumnSettings 的 _updateKey 变化
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log(
        `[TableView] useEffect 监听 _updateKey 变化 - usePersistentColumns: ${usePersistentColumns}, _updateKey: ${persistentColumnSettings._updateKey}`
      );
    }

    if (usePersistentColumns && persistentColumnSettings._updateKey > 0) {
      setTableViewUpdateKey((prev) => prev + 1);
      if (process.env.NODE_ENV === 'development') {
        console.log(
          `[TableView] 检测到 persistentColumnSettings 变化，强制刷新 TableView - updateKey: ${persistentColumnSettings._updateKey}`
        );
      }
    }
  }, [persistentColumnSettings._updateKey, usePersistentColumns]);

  // 额外的监听机制：直接监听 persistentColumnSettings 对象的变化
  useEffect(() => {
    if (
      usePersistentColumns &&
      persistentColumnSettings &&
      persistentColumnSettings._updateKey > 0
    ) {
      // 使用微任务确保在下一个渲染周期中触发更新
      Promise.resolve().then(() => {
        setTableViewUpdateKey((prev) => {
          const newKey = prev + 1;
          if (process.env.NODE_ENV === 'development') {
            console.log(
              `[TableView] 微任务中检测到 persistentColumnSettings 变化，强制刷新 - 新的updateKey: ${newKey}`
            );
          }
          return newKey;
        });
      });
    }
  }, [persistentColumnSettings, usePersistentColumns]);

  // 特定监听列可见性和顺序变化
  useEffect(() => {
    if (usePersistentColumns && persistentColumnSettings) {
      if (process.env.NODE_ENV === 'development') {
        console.log(
          `[TableView] 列设置变化监听 - visibleColumns长度: ${persistentColumnSettings.visibleColumns?.length}, columnVisibility:`,
          persistentColumnSettings.columnVisibility
        );
      }

      // 强制触发TableView重新渲染
      setTableViewUpdateKey((prev) => {
        const newKey = prev + 1;
        if (process.env.NODE_ENV === 'development') {
          console.log(`[TableView] 列设置变化，强制刷新TableView - updateKey: ${newKey}`);
        }
        return newKey;
      });
    }
  }, [
    usePersistentColumns,
    persistentColumnSettings?.visibleColumns?.length,
    persistentColumnSettings?.columnVisibility,
    persistentColumnSettings?.columnOrder,
  ]);

  // 额外的监听：直接监听 persistentColumnSettings 的所有变化
  useEffect(() => {
    if (usePersistentColumns && persistentColumnSettings) {
      if (process.env.NODE_ENV === 'development') {
        console.log(
          `[TableView] 检测到 persistentColumnSettings 整体变化 - rawSettings:`,
          persistentColumnSettings.rawSettings
        );
      }

      // 使用 setTimeout 确保在下一个渲染周期触发
      setTimeout(() => {
        setTableViewUpdateKey((prev) => {
          const newKey = prev + 1;
          if (process.env.NODE_ENV === 'development') {
            console.log(`[TableView] 延迟触发 TableView 重新渲染 - updateKey: ${newKey}`);
          }
          return newKey;
        });
      }, 0);
    }
  }, [usePersistentColumns, persistentColumnSettings]);

  // 选择使用持久化还是内存状态的可见列
  const visibleColumns = useMemo(() => {
    const result = usePersistentColumns
      ? persistentColumnSettings.visibleColumns
      : columnManager.visibleColumns;

    // 在开发环境添加调试信息
    if (process.env.NODE_ENV === 'development' && tableId) {
      console.log(
        `[TableView] 计算 visibleColumns - usePersistentColumns: ${usePersistentColumns}`
      );
      console.log(
        `[TableView] persistentColumnSettings._updateKey: ${persistentColumnSettings._updateKey}`
      );
      console.log(`[TableView] tableViewUpdateKey: ${tableViewUpdateKey}`);
      console.log(
        `[TableView] persistentColumnSettings.visibleColumns:`,
        persistentColumnSettings.visibleColumns
      );
      console.log(`[TableView] columnManager.visibleColumns:`, columnManager.visibleColumns);
      console.log(`[TableView] 最终 visibleColumns:`, result);
      console.log(`[TableView] 最终 visibleColumns 长度: ${result?.length}`);
    }

    return result;
  }, [
    usePersistentColumns,
    persistentColumnSettings, // 依赖整个对象
    persistentColumnSettings._updateKey, // 显式依赖 _updateKey
    persistentColumnSettings.visibleColumns, // 显式依赖 visibleColumns
    persistentColumnSettings.columnVisibility, // 显式依赖 columnVisibility
    persistentColumnSettings.columnOrder, // 显式依赖 columnOrder
    columnManager.visibleColumns,
    tableViewUpdateKey, // 添加强制刷新依赖
    tableId,
  ]);

  // 计算表格总宽度 - 响应式优化（使用可见列）
  const totalTableWidth = useMemo(() => {
    // 获取容器的实际宽度，优先使用100%
    const containerWidth = window.innerWidth;

    let calculatedWidth = 0;

    // 添加选择列的宽度
    if (enableSelection) {
      calculatedWidth += 58; // checkbox列的宽度
    }

    // 添加每个可见列的宽度
    visibleColumns.forEach((column) => {
      if (column.width) {
        calculatedWidth += column.width;
      } else if (column.minWidth) {
        calculatedWidth += column.minWidth;
      } else {
        calculatedWidth += 120; // 默认列宽
      }
    });

    // 计算可用空间（考虑卡片内边距和滚动条）
    const availableWidth = containerWidth - 80; // 预留80px给边距和滚动条

    // 如果计算宽度小于可用宽度，使用100%以充分利用空间
    // 否则使用计算宽度以启用水平滚动
    return calculatedWidth < availableWidth ? '100%' : calculatedWidth;
  }, [visibleColumns, enableSelection]);

  // 从搜索Hook中获取防抖搜索值
  const { searchValue, debouncedSearchValue } = tableSearch;

  // 使用外部loading状态或内部状态
  const isLoading = externalLoading !== undefined ? externalLoading : loading;

  // 支持排序和搜索参数，调用方可选择性处理
  const fetchData = useCallback(async () => {
    if (typeof getData !== 'function') {
      setLoading(false);
      setTableData([]);
      setTotalCount(0);
      setShowNoData(true);
      console.error('TableView: getData 未传递或不是一个函数');
      return;
    }
    try {
      setLoading(true);
      // 清空之前的无数据状态，避免闪现
      setShowNoData(false);

      let data, total;
      // 传递搜索参数给getData函数
      [data, total] = await getData(
        table.page,
        table.rowsPerPage,
        table.orderBy,
        table.order,
        debouncedSearchValue
      );

      // 原子性更新所有状态
      setTableData(data || []);
      setTotalCount(total || 0);
      setShowNoData((data || []).length === 0);
    } catch (error) {
      console.error('获取表格数据失败:', error);
      setTableData([]);
      setTotalCount(0);
      setShowNoData(true);
    } finally {
      setLoading(false);
    }
  }, [getData, table.page, table.rowsPerPage, table.orderBy, table.order, debouncedSearchValue]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 暴露给父组件的方法
  useImperativeHandle(
    ref,
    () => ({
      refetch: fetchData,
    }),
    [fetchData]
  );

  useEffect(() => {
    if (onSelectionChange) {
      onSelectionChange(table.selected);
    }
  }, [table.selected, onSelectionChange]);

  // 使用 useRef 来稳定函数引用，避免不必要的重新渲染
  const selectRowRef = useRef(table.onSelectRow);
  selectRowRef.current = table.onSelectRow;

  const handleSelectRow = useCallback((id) => {
    selectRowRef.current(id);
    // onSelectionChange will be called by the new useEffect
  }, []);

  // 使用 useRef 来稳定删除相关的函数引用
  const deleteRowsRef = useRef({
    onDelete,
    selected: table.selected,
    onSelectAllRows: table.onSelectAllRows,
    fetchData,
    onFalse: confirmDialog.onFalse,
  });
  deleteRowsRef.current = {
    onDelete,
    selected: table.selected,
    onSelectAllRows: table.onSelectAllRows,
    fetchData,
    onFalse: confirmDialog.onFalse,
  };

  const handleDeleteRows = useCallback(async () => {
    const {
      onDelete: currentOnDelete,
      selected,
      onSelectAllRows,
      fetchData: currentFetchData,
      onFalse,
    } = deleteRowsRef.current;
    if (currentOnDelete && selected.length > 0) {
      try {
        await currentOnDelete(selected);
        onSelectAllRows(false, []); // 清空选择
        currentFetchData(); // 重新获取数据
      } catch (error) {
        console.error('批量删除失败:', error);
      }
    }
    onFalse();
  }, []);

  // 当防抖动搜索值变化时，清空选中项和重置页码
  const prevSearchValueRef = useRef(debouncedSearchValue);
  const clearSelectionRef = useRef(table.onSelectAllRows);
  clearSelectionRef.current = table.onSelectAllRows;

  useEffect(() => {
    // 只有当搜索值真正变化时才清空选择，避免选择状态变化时误触发
    if (prevSearchValueRef.current !== debouncedSearchValue) {
      if (table.selected.length > 0) {
        clearSelectionRef.current(false, []);
      }
      prevSearchValueRef.current = debouncedSearchValue;
    }
  }, [debouncedSearchValue, table.selected.length]);

  // 扩展搜索处理函数以包含页码重置
  const handleSearchWithReset = useCallback(
    (event) => {
      tableSearch.handleSearch(event);
      // 立即重置页码，不等待防抖动
      table.onResetPage();
    },
    [tableSearch, table]
  );

  const handleClearSearchWithReset = useCallback(() => {
    tableSearch.handleClearSearch();
    table.onResetPage();
    table.onSelectAllRows(false, []); // 清空选中项
  }, [tableSearch, table]);

  // 缓存分页选项计算
  const paginationOptions = useMemo(() => {
    if (is4K) return [25, 50, 75, 100];
    if (is2K) return [20, 30, 50, 75];
    if (isXL) return [15, 25, 40, 60];
    if (isDesktop) return [10, 20, 30, 50];
    return [5, 10, 15, 25];
  }, [is4K, is2K, isXL, isDesktop]);

  // 缓存总页数计算
  const totalPages = useMemo(
    () => Math.ceil(totalCount / table.rowsPerPage),
    [totalCount, table.rowsPerPage]
  );

  // 缓存列数计算 - 使用可见列计算
  const totalColumns = useMemo(
    () => visibleColumns.length + (enableSelection ? 1 : 0),
    [visibleColumns.length, enableSelection]
  );

  /**
   * 渲染表格行，要求数据主键字段为 ID 或 id
   */
  const renderTableRows = useCallback(() => {
    if (showNoData) return null;
    return tableData.map((row, index) => {
      const rowKey = row.ID || row.id || `row-${index}`;
      const selected = table.selected.includes(rowKey);
      return (
        <TableRow
          hover
          key={rowKey}
          selected={selected}
          onClick={() => onRowClick && onRowClick(row)}
          sx={{
            cursor: onRowClick ? 'pointer' : 'default',
            '&:hover': onRowClick ? { backgroundColor: 'action.hover' } : {},
          }}
        >
          {enableSelection && (
            <TableCell
              padding="checkbox"
              sx={{
                position: 'sticky',
                left: 0,
                backgroundColor: 'background.paper',
                zIndex: 1, // 比普通单元格高，但比表头低
              }}
            >
              <Checkbox
                checked={selected}
                onClick={(e) => {
                  e.stopPropagation();
                  handleSelectRow(rowKey);
                }}
                slotProps={{
                  input: {
                    'aria-labelledby': `row-${rowKey}-checkbox`,
                    'aria-label': `选择第${index + 1}行`,
                  },
                }}
              />
            </TableCell>
          )}
          {visibleColumns.map((column, columnIndex) => {
            const isLastColumn = columnIndex === visibleColumns.length - 1;
            const isActionColumn = column.id === 'action' || column.id === 'actions';
            const shouldStick = enableStickyLastColumn && (isLastColumn || isActionColumn);

            return (
              <TableCell
                key={`${rowKey}-${column.id}`}
                align={column.align || 'left'}
                sx={{
                  // 强制尺寸设置 - 确保固定表格布局生效
                  ...(column.width && {
                    width: column.width,
                    maxWidth: column.width,
                    minWidth: column.width,
                  }),
                  ...(column.minWidth && !column.width && { minWidth: column.minWidth }),
                  ...(column.maxWidth && { maxWidth: column.maxWidth }),

                  // 强制文本截断样式 - 对所有单元格都应用
                  whiteSpace: 'nowrap',
                  overflow: 'hidden !important',
                  textOverflow: 'ellipsis',

                  // 确保内容不会溢出到其他单元格
                  '& *': {
                    maxWidth: '100%',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  },

                  // 确保在悬停状态下样式不被覆盖
                  '&:hover': {
                    overflow: 'hidden !important',
                    textOverflow: 'ellipsis',
                    '& *': {
                      overflow: 'hidden !important',
                      textOverflow: 'ellipsis',
                    },
                  },

                  // 固定最后一列的样式
                  ...(shouldStick && {
                    position: 'sticky',
                    right: 0,
                    backgroundColor: 'background.paper',
                    zIndex: 1,
                    // 使用 box-shadow 创建左边分隔线，确保在所有行都显示
                    boxShadow: 'inset 1px 0 0 0 rgba(145, 158, 171, 0.16)',
                  }),
                }}
              >
                {column.renderCell ? column.renderCell(row, index) : row[column.id]}
              </TableCell>
            );
          })}
        </TableRow>
      );
    });
  }, [
    showNoData,
    tableData,
    table.selected,
    onRowClick,
    enableSelection,
    handleSelectRow,
    visibleColumns,
    enableStickyLastColumn,
  ]);

  // 使用 useMemo 缓存确认对话框
  const confirmDialogContent = useMemo(
    () => (
      <ConfirmDialog
        open={confirmDialog.value}
        onClose={confirmDialog.onFalse}
        title={t('common:action.delete')}
        content={<>{t('common:tips.delete', { count: table.selected.length })}</>}
        onConfirm={handleDeleteRows}
        action={
          <Button variant="contained" color="primary" onClick={handleDeleteRows}>
            {t('common:action.delete')}
          </Button>
        }
      />
    ),
    [confirmDialog.value, confirmDialog.onFalse, table.selected.length, handleDeleteRows, t]
  );

  // 当无数据且加载完成时，只显示无数据界面
  if (showNoData && !isLoading) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', pb: 2 }}>
        {/* 无数据界面 - 居中显示，无背景 */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 400,
          }}
        >
          <TableNoData
            notFound
            title={noDataText}
            sx={{ py: 10, backgroundColor: 'transparent' }}
          />
        </Box>
      </Box>
    );
  }

  // 在初始加载状态时，显示 loading 界面而不是表格结构
  if (isLoading && tableData.length === 0) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', pb: 2 }}>
        <Card sx={{ display: 'flex', flexDirection: 'column', flex: 1, mb: 2 }}>
          <Box
            sx={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: 400,
            }}
          >
            <Box sx={{ textAlign: 'center' }}>
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  margin: '0 auto 16px',
                  animation: 'spin 1s linear infinite',
                  '@keyframes spin': {
                    '0%': { transform: 'rotate(0deg)' },
                    '100%': { transform: 'rotate(360deg)' },
                  },
                }}
              >
                <Iconify icon="eos-icons:bubble-loading" sx={{ width: '100%', height: '100%' }} />
              </Box>
              <Typography variant="body2" color="text.secondary">
                {t('common:loading')}
              </Typography>
            </Box>
          </Box>
        </Card>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', pb: 2 }}>
      <Card sx={{ display: 'flex', flexDirection: 'column', flex: 1, mb: 2 }}>
        <Box sx={{ position: 'relative' }}>
          {/* 搜索框区域 */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: 2,
              // 当有选中项时，隐藏搜索框
              ...(enableSelection && table.selected.length > 0 && { display: 'none' }),
            }}
          >
            <TableSearchBar
              value={searchValue}
              onChange={handleSearchWithReset}
              onClear={handleClearSearchWithReset}
              loading={isLoading}
              debouncedValue={debouncedSearchValue}
              totalCount={totalCount}
              placeholder={t('common:tips.search')}
              sx={{ flex: 1 }}
            />

            <TableColumnManager
              tableId={tableId}
              columns={
                usePersistentColumns
                  ? persistentColumnSettings.orderedColumns
                  : columnManager.orderedColumns
              }
              visibility={usePersistentColumns ? undefined : columnManager.columnVisibility}
              onVisibilityChange={
                usePersistentColumns ? undefined : columnManager.handleColumnVisibilityChange
              }
              onColumnReorder={usePersistentColumns ? undefined : columnManager.handleColumnReorder}
              anchorEl={columnManager.columnMenuAnchor}
              open={columnManager.columnMenuOpen}
              onClose={columnManager.handleColumnMenuClose}
              onOpen={columnManager.handleColumnMenuOpen}
            />
          </Box>

          {/* 选中操作区域 */}
          {enableSelection && table.selected.length > 0 && (
            <Box
              sx={{
                py: 2.5,
                px: 3,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                gap: 2,
                bgcolor: 'primary.lighter',
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Checkbox
                  indeterminate={
                    !!table.selected.length && table.selected.length < tableData.length
                  }
                  checked={!!tableData.length && table.selected.length === tableData.length}
                  onChange={(event) => {
                    const allIds = tableData.map((row) => row.ID || row.id);
                    table.onSelectAllRows(event.target.checked, allIds);
                  }}
                  slotProps={{
                    input: {
                      id: 'deselect-all-checkbox',
                      'aria-label': 'Deselect all checkbox',
                    },
                  }}
                />
                <Typography
                  variant="subtitle2"
                  sx={{
                    color: 'primary.main',
                    fontWeight: 600,
                  }}
                >
                  {table.selected.length} selected
                </Typography>
              </Box>

              {/* 操作按钮组 */}
              <Box sx={{ display: 'flex', gap: 1 }}>
                {onDelete && (
                  <Button
                    size="small"
                    color="error"
                    variant="text"
                    startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
                    onClick={confirmDialog.onTrue}
                  >
                    {t('common:action.delete')}
                  </Button>
                )}
                {selectedActions.map((actionItem) => (
                  <Button
                    key={actionItem.key}
                    size="small"
                    color={actionItem.color || 'primary'}
                    variant={actionItem.variant || 'text'}
                    startIcon={actionItem.icon && <Iconify icon={actionItem.icon} />}
                    onClick={() => actionItem.onClick(table.selected)}
                    {...(actionItem.buttonProps || {})}
                  >
                    {actionItem.label}
                  </Button>
                ))}
              </Box>
            </Box>
          )}
        </Box>
        <Box sx={{ overflow: 'hidden' }}>
          <Scrollbar
            sx={{
              // 响应式表格高度优化 - 考虑DashboardContent底部内边距(64px)
              height: {
                xs: 'calc(100vh - 384px)', // 320 + 64 = 384px
                sm: 'calc(100vh - 364px)', // 300 + 64 = 364px
                md: 'calc(100vh - 344px)', // 280 + 64 = 344px
                lg: 'calc(100vh - 324px)', // 260 + 64 = 324px
                xl: 'calc(100vh - 304px)', // 240 + 64 = 304px
              },
              maxHeight: {
                xs: '400px',
                sm: '500px',
                md: '600px',
                lg: '800px',
                xl: '1000px', // 适当限制最大高度，避免过高
              },
              minHeight: '300px',
              // 确保横向和纵向滚动正常工作
              overflow: 'auto',
              width: '100%',
              '& .simplebar-content-wrapper': {
                overflow: 'auto !important',
              },
              '& .simplebar-content': {
                height: '100% !important',
                width: typeof totalTableWidth === 'number' ? 'max-content' : '100%',
              },
            }}
          >
            <Table
              stickyHeader
              size={table.dense ? 'small' : 'medium'}
              sx={{
                ...(typeof totalTableWidth === 'number'
                  ? {
                      minWidth: totalTableWidth,
                      width: totalTableWidth,
                      tableLayout: 'fixed', // 固定布局用于精确控制列宽
                    }
                  : {
                      width: '100%', // 使用全宽以充分利用空间
                      tableLayout: 'auto', // 自动布局以适应内容
                    }),
                // 自定义行高 - 优化空间利用率
                '& .MuiTableRow-root': {
                  height: table.dense ? 55 : 58, // 优化后：dense 52px，normal 58px
                },
                '& .MuiTableCell-root': {
                  padding: table.dense ? '12px 16px' : '14px 16px', // 增加内边距提升视觉效果
                },
              }}
              {...tableProps}
            >
              <TableHeadCustom
                order={table.order}
                orderBy={table.orderBy}
                headCells={visibleColumns}
                rowCount={tableData.length}
                numSelected={table.selected.length}
                onSort={table.onSort}
                onSelectAllRows={
                  enableSelection
                    ? (checked) => {
                        const allIds = tableData.map((row) => row.ID || row.id);
                        table.onSelectAllRows(checked, allIds);
                        // onSelectionChange will be called by the new useEffect
                      }
                    : null
                }
                enableStickyLastColumn={enableStickyLastColumn}
              />
              <TableBody>
                {isLoading ? (
                  <TableSkeleton
                    rowCount={5} // Assuming 5 rows for skeleton as implied by original sx height
                    cellCount={totalColumns}
                  />
                ) : (
                  <>
                    {showNoData ? (
                      <TableNoData
                        notFound
                        title={noDataText}
                        colSpan={totalColumns}
                        sx={{ py: 10 }} // This sx is passed to EmptyContent inside TableNoData
                      />
                    ) : (
                      renderTableRows()
                    )}
                  </>
                )}
              </TableBody>
            </Table>
          </Scrollbar>
        </Box>
        <Box
          sx={{
            p: { xs: 1, sm: 2 },
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderTop: 1,
            borderColor: 'divider',
            flexDirection: { xs: 'column', md: 'row' },
            gap: { xs: 1, sm: 2 },
          }}
        >
          {/* 左侧控制区域 */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: { xs: 1, sm: 2 },
              flexDirection: { xs: 'column', sm: 'row' },
              width: { xs: '100%', md: 'auto' },
            }}
          >
            {/* Dense 开关和每页行数控制 */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 1, sm: 2 } }}>
              <FormControlLabel
                label="Dense"
                control={
                  <Switch checked={table.dense} onChange={table.onChangeDense} size="small" />
                }
                sx={{ mr: 0 }}
              />
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2" color="text.secondary" sx={{ whiteSpace: 'nowrap' }}>
                  {t('common:table.rowsPerPage')}:
                </Typography>
                <FormControl size="small" sx={{ minWidth: 60 }}>
                  <Select
                    value={table.rowsPerPage}
                    onChange={(event) => {
                      setHasUserSelectedRowsPerPage(true); // 标记用户已手动选择
                      table.onChangeRowsPerPage(event);
                    }}
                    sx={{
                      '& .MuiSelect-select': {
                        py: 0.5,
                        px: 1,
                      },
                    }}
                  >
                    {paginationOptions.map((option) => (
                      <MenuItem key={option} value={option}>
                        {option}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Box>

            {/* 数据统计信息 */}
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                whiteSpace: 'nowrap',
                display: { xs: 'none', sm: 'block' }, // 在小屏幕隐藏统计信息
              }}
            >
              {t('common:table.showing')} {table.page * table.rowsPerPage + 1}-
              {Math.min((table.page + 1) * table.rowsPerPage, totalCount)} {t('common:table.of')}{' '}
              {totalCount}
            </Typography>
          </Box>

          {/* 右侧分页区域 */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: { xs: 'center', md: 'flex-end' },
              width: { xs: '100%', md: 'auto' },
            }}
          >
            <Pagination
              count={totalPages}
              page={table.page + 1} // MUI Pagination 从 1 开始，useTable 从 0 开始
              onChange={(event, page) => table.onChangePage(event, page - 1)} // 转换回 0 索引
              color="primary"
              size="small"
              showFirstButton={totalPages > 7} // 只在页数较多时显示首尾按钮
              showLastButton={totalPages > 7}
              siblingCount={0} // 减少显示的页码数量
              boundaryCount={1}
              sx={{
                '& .MuiPagination-ul': {
                  flexWrap: 'nowrap',
                  justifyContent: 'center',
                },
              }}
            />
          </Box>
        </Box>
      </Card>

      {/* 只有提供了删除回调时才显示确认对话框 */}
      {onDelete && confirmDialogContent}
    </Box>
  );
});
